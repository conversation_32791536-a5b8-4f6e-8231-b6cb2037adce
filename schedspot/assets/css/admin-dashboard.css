/**
 * SchedSpot Admin Dashboard Styles
 *
 * @package SchedSpot
 * @version 1.0.0
 */

/* Dashboard Widgets */
.schedspot-dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.schedspot-widget {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.schedspot-widget h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #23282d;
}

/* Statistics Grid */
.schedspot-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Quick Actions */
.schedspot-quick-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Placeholder Styles */
.schedspot-placeholder {
    text-align: center;
    padding: 40px;
    background: #f9f9f9;
    border-radius: 4px;
    margin-top: 20px;
}

/* Role Switcher */
.schedspot-role-switcher {
    max-width: 800px;
}

.current-role-info {
    background: #f1f1f1;
    padding: 20px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.role-descriptions {
    margin-top: 30px;
}

.role-description {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.role-description h4 {
    margin-top: 0;
    color: #0073aa;
}

.role-capabilities {
    margin-top: 10px;
}

.role-capabilities ul {
    margin: 5px 0 0 20px;
}

.role-capabilities li {
    margin-bottom: 3px;
}

.switch-role-form {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
}

.switch-role-form h3 {
    margin-top: 0;
}

.role-selection {
    margin: 15px 0;
}

.role-selection label {
    display: block;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.role-selection label:hover {
    background-color: #f9f9f9;
}

.role-selection input[type="radio"] {
    margin-right: 10px;
}

.role-selection .role-name {
    font-weight: bold;
}

.role-selection .role-desc {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Booking Information Grid */
.booking-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.booking-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
}

.booking-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #0073aa;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.booking-details {
    display: grid;
    gap: 10px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #f5f5f5;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: bold;
    color: #333;
}

.detail-value {
    color: #666;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-completed {
    background: #cce5ff;
    color: #004085;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Booking Messages */
.booking-messages {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
}

.message-item {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 5px;
}

.worker-message {
    background: #e3f2fd;
    margin-left: 20px;
}

.customer-message {
    background: #f3e5f5;
    margin-right: 20px;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
}

.message-header strong {
    color: #0073aa;
}

.message-time {
    color: #666;
}

.message-content {
    line-height: 1.4;
}

/* Booking Timeline */
.booking-timeline {
    position: relative;
    padding-left: 30px;
}

.booking-timeline::before {
    content: "";
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #ddd;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-marker.status-pending {
    background: #ffc107;
}

.timeline-marker.status-confirmed {
    background: #28a745;
}

.timeline-marker.status-completed {
    background: #007bff;
}

.timeline-marker.status-cancelled {
    background: #dc3545;
}

.timeline-content {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.timeline-header strong {
    color: #0073aa;
}

.timeline-date {
    color: #666;
    font-size: 12px;
}

.timeline-description {
    color: #333;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .schedspot-dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .schedspot-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .booking-info-grid {
        grid-template-columns: 1fr;
    }
    
    .detail-row {
        flex-direction: column;
        gap: 5px;
    }
    
    .timeline-header {
        flex-direction: column;
        gap: 5px;
    }
}

/* Enhanced Role Switcher Styles */
.schedspot-role-switcher-container {
    max-width: 1200px;
    margin: 20px 0;
}

.current-role-display {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.current-role-display h2 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 300;
}

.current-role-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.2);
    padding: 15px 25px;
    border-radius: 50px;
    margin: 15px 0;
    backdrop-filter: blur(10px);
}

.current-role-badge .role-icon {
    font-size: 24px;
    margin-right: 12px;
}

.current-role-badge .role-name {
    font-size: 20px;
    font-weight: 600;
}

.role-description {
    margin: 15px 0 0 0;
    font-size: 16px;
    opacity: 0.9;
}

.role-switcher-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.role-option {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.role-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #0073aa;
}

.role-option.active {
    border-color: #00a32a;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,163,42,0.2);
}

.role-option.active::before {
    content: '✓';
    position: absolute;
    top: 15px;
    right: 15px;
    background: #00a32a;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

.role-header {
    text-align: center;
    margin-bottom: 20px;
}

.role-icon-large {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.role-header h3 {
    margin: 0;
    font-size: 22px;
    color: #1d2327;
    font-weight: 600;
}

.role-features ul {
    list-style: none;
    padding: 0;
    margin: 0 0 25px 0;
}

.role-features li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
    position: relative;
    padding-left: 20px;
}

.role-features li:last-child {
    border-bottom: none;
}

.role-features li::before {
    content: '•';
    color: #0073aa;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.role-actions {
    text-align: center;
}

.role-switch-btn {
    background: linear-gradient(135deg, #0073aa 0%, #005177 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 140px;
}

.role-switch-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #005177 0%, #003d5c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,115,170,0.3);
}

.role-switch-btn.current {
    background: linear-gradient(135deg, #00a32a 0%, #007a1f 100%);
    cursor: default;
}

.role-switch-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}
